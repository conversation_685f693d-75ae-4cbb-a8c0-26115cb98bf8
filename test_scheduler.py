#!/usr/bin/env python3
"""
Profit Data Scheduler 测试脚本
"""

import asyncio
import os
from datetime import datetime, date, timedelta
from utils.app.profit_data_scheduler_mysql import ProfitDataScheduler, apply_basic_data_cleaning

async def test_basic_functionality():
    """测试基础功能"""
    print("=== 测试 Profit Data Scheduler 基础功能 ===")
    
    # 设置测试环境变量
    os.environ['PRO2_SYSTEM_ID'] = '86021'  # 设置一个测试系统ID
    
    try:
        # 测试1: 创建调度器实例
        print("\n1. 测试调度器实例化:")
        scheduler = ProfitDataScheduler()
        print(f"✓ 调度器创建成功，系统ID: {scheduler.pro2_system_id}")
        
        # 测试2: 测试数据清理函数
        print("\n2. 测试数据清理函数:")
        test_data = {
            'profit': '1500.50',
            'operator_name': '  张三  ',
            'empty_field': '',
            'null_field': None,
            'nan_field': 'NaN',
            'date_field': '2025-01-15',
            'invalid_date': '0000-00-00'
        }
        
        cleaned_data = apply_basic_data_cleaning(test_data)
        print(f"✓ 原始数据: {test_data}")
        print(f"✓ 清理后数据: {cleaned_data}")
        
        # 测试3: 测试数据库连接（如果配置了数据库）
        print("\n3. 测试数据库连接:")
        try:
            await scheduler.ensure_required_tables_exist()
            print("✓ 数据库连接和表创建测试成功")
        except Exception as e:
            print(f"⚠ 数据库连接测试失败（可能是环境配置问题）: {e}")
        
        # 测试4: 测试周期配置
        print("\n4. 测试分析周期配置:")
        from utils.app.profit_data_scheduler_mysql import AnalysisPeriod
        
        period = AnalysisPeriod(
            start_date=date(2025, 1, 1),
            end_date=date(2025, 1, 31),
            period_type="current",
            check_frequency="1week"
        )
        print(f"✓ 分析周期创建成功: {period}")
        
        print("\n=== 基础功能测试完成 ===")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_data_processing():
    """测试数据处理函数"""
    print("\n=== 测试数据处理函数 ===")
    
    # 测试数据清理
    test_cases = [
        {
            'input': {'profit': 'NaN', 'name': '  test  ', 'value': None},
            'expected_changes': {'profit': None, 'name': 'test'}
        },
        {
            'input': {'date_field': '2025-01-15', 'invalid_date': '0000-00-00'},
            'expected_changes': {'invalid_date': None}
        },
        {
            'input': {'empty_str': '', 'null_str': 'null', 'na_str': 'n/a'},
            'expected_changes': {'empty_str': None, 'null_str': None, 'na_str': None}
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        result = apply_basic_data_cleaning(case['input'])
        print(f"  输入: {case['input']}")
        print(f"  输出: {result}")
        
        # 验证预期变化
        for key, expected_value in case['expected_changes'].items():
            if result.get(key) == expected_value:
                print(f"  ✓ {key} 处理正确")
            else:
                print(f"  ✗ {key} 处理错误，期望: {expected_value}, 实际: {result.get(key)}")

async def test_environment_check():
    """测试环境检查"""
    print("\n=== 环境配置检查 ===")
    
    required_env_vars = [
        'PRO2_SYSTEM_ID',
        'MYSQL_HOST',
        'MYSQL_USER', 
        'MYSQL_PASSWORD',
        'MYSQL_DB_CMSDATA'
    ]
    
    print("检查必需的环境变量:")
    for var in required_env_vars:
        value = os.getenv(var)
        if value:
            if 'PASSWORD' in var:
                print(f"  ✓ {var}: ***")
            else:
                print(f"  ✓ {var}: {value}")
        else:
            print(f"  ⚠ {var}: 未设置")

async def main():
    """主测试函数"""
    print("开始 Profit Data Scheduler 测试")
    print("=" * 50)
    
    # 环境检查
    await test_environment_check()
    
    # 数据处理测试
    test_data_processing()
    
    # 基础功能测试
    await test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())