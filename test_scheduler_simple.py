#!/usr/bin/env python3
"""
Profit Data Scheduler 简化测试
"""

import asyncio
import os
from datetime import datetime, date, timedelta
from utils.app.profit_data_scheduler_mysql import ProfitDataScheduler, AnalysisPeriod, apply_basic_data_cleaning

async def test_basic_functions():
    """测试基础功能"""
    print("=== 测试调度器基础功能 ===")
    
    # 设置测试环境变量
    os.environ['PRO2_SYSTEM_ID'] = '86021'
    
    try:
        # 1. 测试调度器初始化
        print("1. 测试调度器初始化:")
        scheduler = ProfitDataScheduler()
        print(f"   ✓ 调度器创建成功，系统ID: {scheduler.pro2_system_id}")
        
        # 2. 测试数据库表创建
        print("\n2. 测试数据库表创建:")
        await scheduler.ensure_required_tables_exist()
        print("   ✓ 数据库表创建/验证成功")
        
        # 3. 测试分析周期生成
        print("\n3. 测试分析周期生成:")
        periods = scheduler.get_analysis_periods()
        print(f"   ✓ 生成了 {len(periods)} 个分析周期")
        
        # 显示各种类型的周期
        period_types = {}
        for period in periods:
            if period.period_type not in period_types:
                period_types[period.period_type] = []
            period_types[period.period_type].append(period)
        
        for ptype, plist in period_types.items():
            print(f"   - {ptype}: {len(plist)} 个周期")
            if plist:
                print(f"     示例: {plist[0].start_date} ~ {plist[0].end_date}")
        
        # 4. 测试单个周期的检查逻辑
        print("\n4. 测试周期检查逻辑:")
        current_periods = [p for p in periods if p.period_type == "current"]
        if current_periods:
            test_period = current_periods[0]
            print(f"   测试周期: {test_period.start_date} ~ {test_period.end_date}")
            
            # 检查是否需要处理
            needs_check = scheduler.should_check_period(test_period)
            print(f"   是否需要检查: {needs_check}")
            
            # 获取最后检查时间
            last_check = await scheduler.get_last_check_time(test_period)
            print(f"   最后检查时间: {last_check}")
        
        # 5. 测试数据清理函数
        print("\n5. 测试数据清理函数:")
        test_data = {
            'profit': '1500.50',
            'operator_name': '  张三  ',
            'empty_field': '',
            'nan_field': 'NaN',
            'date_field': '2025-01-15',
            'invalid_date': '0000-00-00'
        }
        
        cleaned_data = apply_basic_data_cleaning(test_data)
        print(f"   原始数据字段数: {len(test_data)}")
        print(f"   清理后字段数: {len(cleaned_data)}")
        print(f"   日期字段处理: {test_data['date_field']} -> {cleaned_data['date_field']}")
        print(f"   无效日期处理: {test_data['invalid_date']} -> {cleaned_data['invalid_date']}")
        
        print("\n✓ 所有基础功能测试通过")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_environment_validation():
    """测试环境变量验证"""
    print("\n=== 测试环境变量验证 ===")
    
    # 备份原始环境变量
    original_env = os.environ.copy()
    
    try:
        # 测试缺少环境变量的情况
        print("1. 测试缺少关键环境变量:")
        os.environ.pop('PRO2_SYSTEM_ID', None)
        
        try:
            scheduler = ProfitDataScheduler()
            print("   ✗ 应该抛出异常但没有")
        except ValueError as e:
            print(f"   ✓ 正确抛出异常: {e}")
        
        # 恢复环境变量
        os.environ.update(original_env)
        os.environ['PRO2_SYSTEM_ID'] = '86021'
        
        # 测试正常初始化
        print("\n2. 测试正常环境变量:")
        scheduler = ProfitDataScheduler()
        print("   ✓ 环境变量验证通过")
        
    except Exception as e:
        print(f"✗ 环境验证测试失败: {e}")
    finally:
        # 恢复原始环境
        os.environ.clear()
        os.environ.update(original_env)

async def main():
    """主测试函数"""
    print("开始 Profit Data Scheduler 简化测试")
    print("=" * 50)
    
    # 环境验证测试
    await test_environment_validation()
    
    # 基础功能测试
    await test_basic_functions()
    
    print("\n" + "=" * 50)
    print("简化测试完成")

if __name__ == "__main__":
    asyncio.run(main())