# PostgreSQL数据库连接模块

import os
import functools
import time
import socket
import asyncio
import atexit
from typing import Dict, List, Union
from random import uniform
from contextlib import contextmanager, asynccontextmanager
from datetime import datetime, date
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import asyncpg
from logger_config import setup_logger

# 加载环境变量
load_dotenv(override=True)

# 使用统一的日志配置

logger = setup_logger(__name__, level="warning")

# 重试装饰器
def retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    连接重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避系数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (BrokenPipeError, ConnectionError, 
                        psycopg2.OperationalError,
                        socket.error, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        # 对于recv相关错误，增加等待时间
                        if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                            wait_time = delay * (backoff ** attempt) + uniform(1.0, 2.0)
                            logger.warning(
                                "[RETRY] %s 网络接收错误 (尝试 %d/%d): %s, %.2f秒后重试",
                                func.__name__, attempt + 1, max_attempts, e, wait_time
                            )
                        else:
                            wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                            logger.warning(
                                "[RETRY] %s 连接失败 (尝试 %d/%d): %s, %.2f秒后重试",
                                func.__name__, attempt + 1, max_attempts, e, wait_time
                            )
                        time.sleep(wait_time)
                    else:
                        logger.error("[RETRY] %s 重试%d次后仍然失败: %s", func.__name__, max_attempts, e)
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error("[RETRY] %s 非连接错误: %s", func.__name__, e)
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

# PostgreSQL数据库配置
PG_HOST = os.getenv("PG_HOST")
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_DB_CMSDATA = os.getenv("PG_DB_CMSDATA", "cmsdata")
PG_PORT = int(os.getenv("PG_PORT", 5432))


class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass


@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def connect_postgres(host: str = None, port: int = None, database: str = None, connect_timeout: int = None) -> psycopg2.extensions.connection:
    """连接PostgreSQL数据库"""
    # 使用提供的参数或默认值
    if host is None:
        host = PG_HOST
    if port is None:
        port = PG_PORT
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    if connect_timeout is None:
        connect_timeout = int(os.getenv('PG_CONNECT_TIMEOUT', '10'))
    
    logger.debug("[PG_CONN] Connecting to PostgreSQL: %s:%d/%s", host, port, database)
    
    try:
        # 增强PostgreSQL连接参数以提高稳定性
        return psycopg2.connect(
            host=host,
            port=port,
            user=PG_USER,
            password=PG_PASSWORD,
            database=database,
            cursor_factory=RealDictCursor,
            connect_timeout=connect_timeout or 600,  # 连接超时600秒
            application_name="cmsdata_app",          # 应用名称标识
            options="-c statement_timeout=600000"     # 语句超时600秒
        )
    except Exception as e:
        raise DatabaseError(f"PostgreSQL连接失败: {str(e)}") from e


@contextmanager
def get_postgres_connection(database: str = None):
    """PostgreSQL连接上下文管理器"""
    conn = None
    try:
        conn = connect_postgres(database=database)
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise DatabaseError(f"PostgreSQL操作失败: {str(e)}")
    finally:
        if conn:
            conn.close()


@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def execute_postgres_query(
    query: str,
    params: tuple = None,
    fetch_all: bool = False,
    auto_commit: bool = True,
    return_last_id: bool = False,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行PostgreSQL查询"""
    conn = None
    cursor = None
    try:
        conn = connect_postgres(database=database)
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if return_last_id:
            # PostgreSQL使用RETURNING子句或序列来获取最后插入的ID
            last_id = cursor.fetchone() if query.upper().strip().endswith('RETURNING *') or 'RETURNING' in query.upper() else None
            if auto_commit:
                conn.commit()
            return last_id
        
        if query.strip().upper().startswith('SELECT'):
            result = cursor.fetchall() if fetch_all else cursor.fetchone()
        else:
            result = cursor.rowcount
        
        if auto_commit:
            conn.commit()
        
        return result
        
    except Exception as e:
        if conn and auto_commit:
            try:
                conn.rollback()
            except Exception:
                pass
        raise DatabaseError(f"PostgreSQL查询执行错误: {str(e)}") from e
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


# 异步支持函数
async def execute_postgres_query_async(
    query: str,
    params: tuple = None,
    fetch_all: bool = False,
    auto_commit: bool = True,
    return_last_id: bool = False,
    database: str = None
):
    """异步执行PostgreSQL查询"""
    return await asyncio.to_thread(
        execute_postgres_query, query, params, fetch_all, auto_commit, return_last_id, database
    )

@asynccontextmanager
async def get_postgres_connection_async(database: str = None):
    """异步PostgreSQL连接上下文管理器"""
    # 使用默认连接参数
    host = PG_HOST
    port = PG_PORT
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    
    logger.debug("[ASYNC_PG_CONN] Connecting to PostgreSQL: %s:%d/%s", host, port, database)
    
    conn = None
    try:
        # 创建异步PostgreSQL连接
        logger.debug(
            "[ASYNC_PG_CONN] 尝试连接参数: host=%s, port=%d, user=%s, database=%s",
            host, port, PG_USER, database
        )
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=PG_USER,
            password=PG_PASSWORD,
            database=database,
            command_timeout=600,  # 增加命令超时时间到600秒
            server_settings={
                'application_name': 'cmsdata_app_async',
                'statement_timeout': '600000'  # 语句超时600秒
            }
        )
        yield conn
    except Exception as e:
        logger.error("[ASYNC_PG_CONN] PostgreSQL连接失败: %s", e)
        logger.error("[ASYNC_PG_CONN] 异常类型: %s", type(e))
        import traceback
        logger.error("[ASYNC_PG_CONN] 详细堆栈: %s", traceback.format_exc())
        raise DatabaseError(f"异步PostgreSQL连接失败: {str(e)}") from e
    finally:
        if conn:
            await conn.close()

# 清理函数
def cleanup_connections():
    """清理所有连接"""
    logger.info("[CLEANUP] Starting connection cleanup")
    # 注意：现在没有需要清理的持久连接，但保留此函数以保持API兼容性
    logger.info("[CLEANUP] Connection cleanup completed")

# 安全格式化日期
def safe_format_date(date_value, format_str='%Y-%m-%d'):
    """安全格式化日期，处理各种日期类型"""
    
    if date_value is None:
        return None
    
    # 检查是否为pandas NaN
    try:
        if pd.isna(date_value):
            return None
    except ImportError:
        # 如果没有pandas，简单检查字符串是否为'nan'或'NaN'
        if isinstance(date_value, str) and date_value.lower() in ['nan', 'nat']:
            return None
    
    # 如果已经是字符串，直接返回（假设已经是正确格式）
    if isinstance(date_value, str):
        # 尝试解析字符串日期并重新格式化
        try:
            # 常见的日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value.strip(), fmt)
                    return parsed_date.strftime(format_str)
                except ValueError:
                    continue
            
            # 如果无法解析，返回原字符串
            return date_value
        except Exception:
            return date_value
    
    # 如果是datetime或date对象
    if isinstance(date_value, (datetime, date)):
        try:
            return date_value.strftime(format_str)
        except Exception:
            return str(date_value)
    
    # 如果是pandas的Timestamp
    if hasattr(date_value, 'strftime'):
        try:
            return date_value.strftime(format_str)
        except Exception:
            return str(date_value)
    
    # 其他情况，尝试转换为字符串
    return str(date_value) if date_value else None

def execute_postgres_script(
    script: str,
    database: str = None,
    auto_commit: bool = True
) -> bool:
    """执行PostgreSQL脚本（多条SQL语句）"""
    
    with get_postgres_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                cursor.execute(script)
                if auto_commit:
                    conn.commit()
                logger.info("PostgreSQL脚本执行成功")
                return True
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise DatabaseError(f"PostgreSQL脚本执行错误: {str(e)}")


def test_postgres_connection(database: str = None) -> bool:
    """测试PostgreSQL连接"""
    try:
        with get_postgres_connection(database=database) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()
                logger.info(f"PostgreSQL连接测试成功: {version['version']}")
                return True
    except Exception as e:
        logger.error(f"PostgreSQL连接测试失败: {str(e)}")
        return False


def get_table_exists(table_name: str, schema: str = 'public', database: str = None) -> bool:
    """检查表是否存在"""
    query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s 
            AND table_name = %s
        );
    """
    
    try:
        result = execute_postgres_query(
            query, 
            params=(schema, table_name), 
            database=database
        )
        return result['exists'] if result else False
    except Exception as e:
        logger.error(f"检查表存在性失败: {str(e)}")
        return False


def drop_table_if_exists(table_name: str, schema: str = 'public', database: str = None) -> bool:
    """删除表（如果存在）"""
    query = f"DROP TABLE IF EXISTS {schema}.{table_name} CASCADE;"
    
    try:
        execute_postgres_query(query, database=database)
        logger.info(f"表 {schema}.{table_name} 删除成功")
        return True
    except Exception as e:
        logger.error(f"删除表失败: {str(e)}")
        return False

atexit.register(cleanup_connections)

# 导出常用函数
__all__ = [
    'DatabaseError',
    'connect_postgres',
    'get_postgres_connection',
    'get_postgres_connection_async',
    'execute_postgres_query',
    'execute_postgres_query_async',
    'execute_postgres_script',
    'test_postgres_connection',
    'get_table_exists',
    'drop_table_if_exists',
    'cleanup_connections',
    'safe_format_date',
    'PG_DB_CMSDATA'
]
