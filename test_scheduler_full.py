#!/usr/bin/env python3
"""
Profit Data Scheduler 完整功能测试
"""

import asyncio
import os
from datetime import datetime, date, timedelta
from utils.app.profit_data_scheduler_mysql import ProfitDataScheduler, AnalysisPeriod

async def test_scheduler_with_real_data():
    """测试调度器处理真实数据的能力"""
    print("=== 测试调度器处理真实数据 ===")
    
    # 设置测试环境变量
    os.environ['PRO2_SYSTEM_ID'] = '86021'
    
    try:
        # 创建调度器
        scheduler = ProfitDataScheduler()
        print(f"✓ 调度器初始化成功，系统ID: {scheduler.pro2_system_id}")
        
        # 测试表创建
        print("\n测试数据库表创建...")
        await scheduler.ensure_required_tables_exist()
        print("✓ 数据库表创建/验证成功")
        
        # 测试分析周期生成
        print("\n测试分析周期生成...")
        periods = scheduler.get_analysis_periods()
        print(f"✓ 生成了 {len(periods)} 个分析周期")
        
        for i, period in enumerate(periods[:3]):  # 只显示前3个
            print(f"  周期 {i+1}: {period.start_date} ~ {period.end_date} ({period.period_type})")
        
        # 测试单个周期处理（使用最近的小周期）
        if periods:
            test_period = None
            for period in periods:
                if period.period_type == "current":
                    # 使用一个很小的时间范围进行测试
                    test_period = AnalysisPeriod(
                        start_date=date.today() - timedelta(days=2),
                        end_date=date.today() - timedelta(days=1),
                        period_type="test",
                        check_frequency="test"
                    )
                    break
            
            if test_period:
                print(f"\n测试处理单个周期: {test_period.start_date} ~ {test_period.end_date}")
                
                # 检查是否需要处理（基于时间）
                last_check = await scheduler.get_last_check_time(test_period)
                print(f"  最后检查时间: {last_check}")
                
                needs_check = scheduler.should_check_period(test_period)
                print(f"  是否需要检查: {needs_check}")
                
                if needs_check:
                    print("  开始处理周期...")
                    try:
                        success = await scheduler.process_period(test_period)
                        if success:
                            print("  ✓ 周期处理成功")
                        else:
                            print("  ⚠ 周期处理失败或无变化")
                    except Exception as e:
                        print(f"  ✗ 周期处理异常: {e}")
                else:
                    print("  ⚠ 周期不需要处理（时间未到或数据未变化）")
        
        print("\n✓ 调度器功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_data_comparison():
    """测试数据对比功能"""
    print("\n=== 测试数据对比功能 ===")
    
    try:
        scheduler = ProfitDataScheduler()
        
        # 测试数据获取
        test_start = "2025-01-01"
        test_end = "2025-01-02"
        
        print(f"测试获取数据: {test_start} ~ {test_end}")
        
        # 测试job数据获取
        print("  获取Job数据...")
        try:
            job_data = await scheduler.get_current_data("job", test_start, test_end)
            print(f"    ✓ 获取Job数据成功: {len(job_data)} 条记录")
            if job_data:
                print(f"    样本数据键: {list(job_data[0].keys())[:5]}...")
        except Exception as e:
            print(f"    ✗ 获取Job数据失败: {e}")
        
        # 测试booking数据获取
        print("  获取Booking数据...")
        try:
            booking_data = await scheduler.get_current_data("booking", test_start, test_end)
            print(f"    ✓ 获取Booking数据成功: {len(booking_data)} 条记录")
            if booking_data:
                print(f"    样本数据键: {list(booking_data[0].keys())[:5]}...")
        except Exception as e:
            print(f"    ✗ 获取Booking数据失败: {e}")
        
    except Exception as e:
        print(f"✗ 数据对比测试失败: {e}")

async def test_hash_calculation():
    """测试数据哈希计算"""
    print("\n=== 测试数据哈希计算 ===")
    
    scheduler = ProfitDataScheduler()
    
    # 测试数据
    test_data = [
        {'id': 1, 'name': 'test1', 'value': 100.5},
        {'id': 2, 'name': 'test2', 'value': 200.0}
    ]
    
    hash1 = scheduler.calculate_data_hash(test_data, "test")
    hash2 = scheduler.calculate_data_hash(test_data, "test")
    print(f"✓ 相同数据的哈希值一致: {hash1 == hash2}")
    
    # 修改数据
    test_data_modified = test_data.copy()
    test_data_modified[0]['value'] = 150.0
    hash3 = scheduler.calculate_data_hash(test_data_modified, "test")
    print(f"✓ 不同数据的哈希值不同: {hash1 != hash3}")
    
    print(f"  原始数据哈希: {hash1}")
    print(f"  修改数据哈希: {hash3}")

async def main():
    """主测试函数"""
    print("开始 Profit Data Scheduler 完整功能测试")
    print("=" * 60)
    
    # 设置测试环境变量
    os.environ['PRO2_SYSTEM_ID'] = '86021'
    
    # 基础功能测试
    await test_hash_calculation()
    
    # 数据对比测试
    await test_data_comparison()
    
    # 调度器完整功能测试
    await test_scheduler_with_real_data()
    
    print("\n" + "=" * 60)
    print("完整功能测试结束")

if __name__ == "__main__":
    asyncio.run(main())